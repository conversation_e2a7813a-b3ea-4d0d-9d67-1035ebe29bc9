import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentRepository } from '@modules/agent/repositories';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';
import {
  CreateTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  UpdateTypeAgentDto,
  GroupToolDto,
} from '../dto';
// TypeAgentStatus không còn được sử dụng
import { AdminGroupToolRepository, UserGroupToolRepository, UserGroupToolsTypeAgentRepository } from '@/modules/tools/repositories';
import { AdminGroupToolsTypeAgents } from '@modules/agent/entities/admin-group-tools-type-agents.entity';

/**
 * Service xử lý các thao tác liên quan đến loại agent cho người dùng
 */
@Injectable()
export class TypeAgentUserService {
  private readonly logger = new Logger(TypeAgentUserService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    @InjectRepository(AdminGroupToolsTypeAgents)
    private readonly adminGroupToolsTypeAgentsRepo: Repository<AdminGroupToolsTypeAgents>,
    private readonly adminGroupToolRepository: AdminGroupToolRepository,
    private readonly userGroupToolsTypeAgentRepository: UserGroupToolsTypeAgentRepository,
    private readonly userGroupToolRepository: UserGroupToolRepository,
  ) {}

  /**
   * Lấy danh sách loại agent có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent có phân trang
   */
  async getTypeAgents(
    userId: number,
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      // Lấy danh sách loại agent của admin đã ACTIVE và loại agent của user
      const result = await this.typeAgentRepository.findPaginatedByQuery(queryDto, userId);

      // Chuyển đổi kết quả sang DTO
      const items = result.items.map((item) => this.mapToTypeAgentListItemDto(item));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED, error.message);
    }
  }

  /**
   * Lấy chi tiết loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   * @returns Chi tiết loại agent
   */
  async getTypeAgentDetail(id: number, userId: number): Promise<TypeAgentDetailDto> {
    try {
      // Lấy thông tin loại agent
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập
      if (typeAgent.userId && typeAgent.userId !== userId) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Lấy danh sách nhóm công cụ của admin
      const adminGroupToolsTypeAgents = await this.adminGroupToolsTypeAgentsRepo.find({
        where: { typeAgentId: id }
      });

      // Lấy danh sách nhóm công cụ của user
      const userGroupToolsTypeAgents = await this.userGroupToolsTypeAgentRepository.findByTypeAgentId(id);

      // Lấy thông tin chi tiết của từng nhóm công cụ của admin
      const groupTools: GroupToolDto[] = [];

      // Xử lý nhóm công cụ của admin
      for (const item of adminGroupToolsTypeAgents) {
        const groupTool = await this.adminGroupToolRepository.findGroupById(item.groupId);
        if (groupTool) {
          groupTools.push({
            id: groupTool.id,
            name: groupTool.name,
            description: groupTool.description,
          });
        }
      }

      // Xử lý nhóm công cụ của user
      for (const item of userGroupToolsTypeAgents) {
        // Lấy thông tin chi tiết của nhóm công cụ
        const userGroupTool = await this.userGroupToolRepository.findGroupById(item.groupId);

        // Chỉ lấy các nhóm công cụ của user hiện tại
        if (userGroupTool && userGroupTool.userId === userId) {
          groupTools.push({
            id: userGroupTool.id,
            name: userGroupTool.name,
            description: userGroupTool.description,
          });
        }
      }

      // Chuyển đổi kết quả sang DTO
      const result = this.mapToTypeAgentDetailDto(typeAgent);
      result.groupTools = groupTools;

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED, error.message);
    }
  }

  /**
   * Tạo loại agent mới
   * @param userId ID của người dùng
   * @param createDto Thông tin loại agent mới
   * @returns ID của loại agent mới
   */
  @Transactional()
  async createTypeAgent(userId: number, createDto: CreateTypeAgentDto): Promise<number> {
    try {
      // Kiểm tra tên loại agent đã tồn tại chưa (theo userId)
      const existingTypeAgent = await this.typeAgentRepository.findOne({
        where: { name: createDto.name, userId: userId, deletedAt: IsNull() }
      });
      if (existingTypeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
      }

      // Kiểm tra các nhóm công cụ có tồn tại không (trong user_group_tools của user này)
      for (const groupToolId of createDto.groupToolIds) {
        const groupTool = await this.userGroupToolRepository.findGroupById(groupToolId, userId);
        if (!groupTool) {
          throw new AppException(AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
        }
      }

      // Tạo loại agent mới
      const savedTypeAgent = await this.typeAgentRepository.createTypeAgent(userId, createDto);

      // Lưu mối quan hệ với nhóm công cụ của user
      for (const groupToolId of createDto.groupToolIds) {
        await this.userGroupToolsTypeAgentRepository.save({
          typeAgentId: savedTypeAgent.id,
          groupId: groupToolId,
        });
      }

      return savedTypeAgent.id;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED, error.message);
    }
  }

  /**
   * Cập nhật loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật
   */
  @Transactional()
  async updateTypeAgent(
    id: number,
    userId: number,
    updateDto: UpdateTypeAgentDto,
  ): Promise<void> {
    try {
      // Lấy thông tin loại agent
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập
      if (typeAgent.userId !== userId) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra tên loại agent đã tồn tại chưa (theo userId)
      if (updateDto.name && updateDto.name !== typeAgent.name) {
        const existingTypeAgent = await this.typeAgentRepository.findOne({
          where: { name: updateDto.name, userId: userId, deletedAt: IsNull() }
        });
        if (existingTypeAgent && existingTypeAgent.id !== id) {
          throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
        }
      }

      // Cập nhật thông tin loại agent
      await this.typeAgentRepository.updateTypeAgent(id, userId, updateDto);

      // Cập nhật mối quan hệ với nhóm công cụ của user
      if (updateDto.groupToolIds) {
        // Kiểm tra các nhóm công cụ có tồn tại không (trong user_group_tools của user này)
        for (const groupToolId of updateDto.groupToolIds) {
          const groupTool = await this.userGroupToolRepository.findGroupById(groupToolId, userId);
          if (!groupTool) {
            throw new AppException(AGENT_ERROR_CODES.GROUP_TOOL_NOT_FOUND);
          }
        }

        // Xóa các mối quan hệ cũ
        await this.userGroupToolsTypeAgentRepository.delete({ typeAgentId: id });

        // Thêm các mối quan hệ mới
        for (const groupToolId of updateDto.groupToolIds) {
          await this.userGroupToolsTypeAgentRepository.save({
            typeAgentId: id,
            groupId: groupToolId,
          });
        }
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteTypeAgent(id: number, userId: number): Promise<void> {
    try {
      // Lấy thông tin loại agent
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập
      if (typeAgent.userId !== userId) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xóa mềm loại agent
      await this.typeAgentRepository.softDeleteTypeAgent(id, userId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED, error.message);
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param typeAgent Entity TypeAgent
   * @returns TypeAgentListItemDto
   */
  private mapToTypeAgentListItemDto(typeAgent: TypeAgent): TypeAgentListItemDto {
    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      config: typeAgent.config,
      createdAt: typeAgent.createdAt,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param typeAgent Entity TypeAgent
   * @returns TypeAgentDetailDto
   */
  private mapToTypeAgentDetailDto(typeAgent: TypeAgent): TypeAgentDetailDto {
    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      config: typeAgent.config,
      updatedAt: typeAgent.updatedAt,
      status: typeAgent.status,
      isSystem: typeAgent.userId === null, // Nếu userId là null thì đây là loại agent của system
      groupTools: [],
    };
  }
}
